import type { QueryClient } from "@tanstack/react-query";
import {
    createRootRouteWithContext,
    HeadContent,
    Outlet,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import { NotFound } from "@/components/404";
import Cursor from "@/components/cursor";
import { authClient } from "@/lib/auth";
import Background from "../components/background";
import Navbar from "../components/navbar";
import appCss from "../index.css?url";

interface RouterContext {
    queryClient: QueryClient;
    user: null;
}

export const Route = createRootRouteWithContext<RouterContext>()({
    component: RootProvider,
    notFoundComponent: () => <NotFound />,
    head: () => ({
        meta: [
            {
                charSet: "utf-8",
            },
            {
                name: "viewport",
                content: "width=device-width, initial-scale=1",
            },
            {
                title: "occo.rocks",
            },
            {
                name: "description",
                content:
                    "i'm just another dev but i have some improvements and some good knowledge i guess :)",
            },
        ],
        links: [{ rel: "stylesheet", href: appCss }],
    }),
    beforeLoad: async ({ context }) => {
        const user = await context.queryClient.fetchQuery({
            queryKey: ["user"],
            queryFn: ({ signal }) => getUser({ signal }),
        }); // we're using react-query for caching, see router.tsx
        return { user };
    },
});

function RootProvider() {
    return (
        <RootDocument>
            <Outlet />
        </RootDocument>
    );
}

function RootDocument({ children }: { readonly children: React.ReactNode }) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                <HeadContent />
            </head>
            <body>
                <Cursor />
                <Navbar />

                {children}

                <Background />
                <TanStackRouterDevtools position="bottom-right" />
            </body>
        </html>
    );
}
